<script>
import accounting from 'accounting';
import EditVariation from './EditVariation.vue';
export default {
    name: 'Variations',
    components: { EditVariation },
    emits: ['update'],
    props: {
        options: {
            type: Array,
            default: () => [],
        },
        variations: {
            type: Array,
            default: () => [],
        },
        media: {
            type: Array,
            default: () => [],
        },
    },
    methods: {
        getPrice(variation) {
            let price = variation.sale_price
                || variation.online_price
                || variation.store_price
                || variation.list_price;
            return '$' + accounting.formatNumber(price, 2);
        },
        getStockQuantity(variation) {
            return variation.store_quantity || '--';
        },
        getFulfillmentQuantity(variation) {
            return variation.website_quantity || '--';
        },
        getSKU(variation) {
            return variation.sku || '--';
        },
        getVendorSKU(variation) {
            return variation.vendor_sku || '--';
        },
        openEditVariation(variation) {
            this.$refs.editVariation.open(variation);
        },
        update(variation) {
            const variations = this.variations.map(v => {
                if (v.id === variation.id) {
                    return variation;
                }
                return v;
            });
            this.$emit('update', variations);
        },
        uploadImage(file) {
            this.$emit('uploadImage', file);
        },
        removeImage(uuid) {
            this.$emit('removeImage', uuid);
        },
    },
};
</script>

<template>
    <div v-if="options && options.length && variations && variations.length" class="px-6 py-5">
        <h1 class="font-normal text-xl md:text-xl mb-3">Variations</h1>
        <table class="table-auto w-full border-collapse variationsTable">
            <thead>
                <tr>
                    <th v-for="option in options">{{ option.name }}</th>
                    <th>Price</th>
                    <th>Stock Quantity</th>
                    <th>Fulfillment Quantity</th>
                    <th>SKU</th>
                    <th>Vendor SKU</th>
                    <th>Action</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="variation in variations">
                    <th v-for="option in options">{{ variation.values[option.name] }}</th>
                    <td>{{ getPrice(variation) }}</td>
                    <td>{{ getStockQuantity(variation) }}</td>
                    <td>{{ getFulfillmentQuantity(variation) }}</td>
                    <td>{{ getSKU(variation) }}</td>
                    <td>{{ getVendorSKU(variation) }}</td>
                    <td>
                        <button
                            @click.prevent="openEditVariation(variation)"
                            class="cursor-pointer rounded text-sm font-bold shadow relative bg-primary-500 hover:bg-primary-400 text-white dark:text-gray-900 h-8 px-3 flex items-center justify-center gap-2"
                        >
                            <span>Edit</span>
                            <Icon type="pencil-alt" solid small />
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
        <edit-variation ref="editVariation" :media="media" @uploadImage="uploadImage" @removeImage="removeImage" :variations="variations" @save="update" />
    </div>
</template>
