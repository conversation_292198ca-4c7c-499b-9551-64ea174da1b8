<template>
    <div>
        <DefaultField
            :field="field"
            :errors="errors"
            :show-help-text="showHelpText"
            :full-width-content="fullWidthContent"
        >
            <template #field>
                <div class="or-products-wrapper">
                    <div
                        class="sing-prod-wrap"
                        v-for="(prod, index) in field.value"
                        :key="index"
                    >
                        <div class="or-single-prod">
                            <div class="or-img">
                                <img :src="prod.media" :alt="prod.title" />
                            </div>
                            <div class="or-prod-info">
                                <a
                                    class="or-prod-title"
                                    :href="'/admin/resources/products/' + prod.id + '/edit'"
                                    target="_blank"
                                    >
                                    {{ prod.title }}
                                </a>
                                <p v-if="prod.giftCardIds" class="or-prod-sku">
                                    Gift Cards<span
                                    @click="giftCard(card.id)"
                                    class="dim or-prod-gc"
                                    v-for="card in prod.giftCardIds"
                                    :key="card"
                                >...{{ card.code }},
                                </span>
                                </p>
                                <p v-if="prod.sku" class="or-prod-sku">
                                    SKU: {{ prod.sku
                                    }}<span class="or-prod-stock"></span>
                                </p>
                                <p v-if="prod.meta" class="or-prod-sku">
                                    {{ metaString(prod.meta) }}
                                </p>
                                <p v-if="prod.personal" class="or-prod-sku">
                                    Personal: {{ prod.personal.string
                                    }}<span class="or-prod-stock"></span>
                                </p>
                                <div v-if="prod.item_type == 'physical'">
                                    <p
                                        style="margin-top: 6px; color: #a8adb4"
                                        class="text-xs text-80"
                                    >
                                        Inventory: Fulfillment -
                                        {{ prod.website_deduct || 0 }}, Store -
                                        {{ prod.store_deduct || 0
                                        }}<span class="or-prod-stock"></span>
                                        <span
                                            @click="openEditInventory(prod)"
                                            style="cursor: pointer"
                                            class="dim or-prod-title-stock"
                                        >Edit</span
                                        >
                                    </p>
                                </div>
                                <p
                                    v-if="prod.returned && prod.returned.string"
                                    class="txt-returned"
                                >
                                    {{ returnedString(prod.returned) }}
                                </p>
                                <p
                                    v-if="prod.status == 'cancelled'"
                                    class="txt-cancelled"
                                >
                                    Cancelled
                                </p>

                                <p v-if="prod.to_name" class="or-prod-sku">
                                    To Name: {{ prod.to_name }}
                                </p>
                                <p v-if="prod.to_email" class="or-prod-sku">
                                    To Email: {{ prod.to_email }}
                                </p>
                                <p v-if="prod.message" class="or-prod-sku">
                                    Message: {{ prod.message.slice(0, 25) }}
                                    {{ prod.message.length > 24 ? '...' : '' }}
                                </p>
                            </div>
                            <p class="or-prod-price">
                                {{ currency(prod.price) }}{{ ' x ' + prod.quantity }}
                            </p>
                            <p class="or-prod-price">
                                {{ currency(prod.price * prod.quantity) }}
                            </p>
                        </div>
                        <div>
                            <p class="gift-option" v-if="prod.gift_options">
                                {{
                                    `Gift Option: ${prod.gift_options.name} | Occasion: ${prod.gift_options.giftNote.name} `
                                }}
                                <a
                                v-if="prod.gift_options.occasionText"
                                :href="'admin/resources/gift-notes/' + prod.gift_options.gift_note_id"
                                target="_blank"
                                class="or-prod-title">
                                    Gift Note
                                </a>
                            </p>
                        </div>
                    </div>
                </div>
            </template>
        </DefaultField>
        <div  v-show="showUpdateInventory">
            <div class="fixed-here">
                <div class="bg-white rounded-lg shadow-lg overflow-hidden box-wrapper-here">
                    <h2 class="border-b border-40 py-8 px-8 text-90 font-normal text-xl">Update Inventory</h2>

                    <div class="flex border-b border-40">
                        <div class="w-1/5 px-8 py-6">
                            <label class="inline-block text-80 pt-2 leading-tight">Store Deduct</label>
                        </div>
                        <div class="py-6 px-8 w-3/4">
                            <input v-model="updateInventoryProd.store_deduct" type="text" placeholder="Store Deduct" class="w-full form-control form-input form-input-bordered">
                        </div>
                    </div>

                    <div class="flex border-b border-40">
                        <div class="w-1/5 px-8 py-6">
                            <label class="inline-block text-80 pt-2 leading-tight">Website Deduct</label>
                        </div>
                        <div class="py-6 px-8 w-3/4">
                            <input v-model="updateInventoryProd.website_deduct" type="text" placeholder="Website Deduct" class="w-full form-control form-input form-input-bordered">
                        </div>
                    </div>

                    <div class="bg-30 px-6 py-3 flex">
                        <div class="flex items-center ml-auto">
                            <button @click.prevent="showUpdateInventory = false" dusk="cancel-action-button" type="button" class="btn btn-link dim cursor-pointer text-80 ml-auto mr-6">Cancel</button>
                            <button @click.prevent="updateInventoryDeduct" dusk="confirm-action-button" type="submit" class="btn btn-default btn-primary"><span>Update Inventory And Resend To POS</span></button>
                        </div>
                    </div>


                </div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { FormField, HandlesValidationErrors } from 'laravel-nova';
import {target} from "vuelidate/lib/params";

export default {
    mixins: [FormField, HandlesValidationErrors],
    props: ['resourceName', 'resourceId', 'field'],
    data() {
        return {
            updateInventoryProd: {},
            showUpdateInventory: false,
        };
    },
    methods: {
        setInitialValue() {
            this.value = this.field.value || '';
        },
        fill(formData) {
            formData.append(this.fieldAttribute, this.value || '');
        },
        returnedString(returned) {
            if (returned.status) {
                return `${_.capitalize(returned.status)} return ${
                    returned.string == '1' ? '' : returned.string
                }`;
            }
        },

        metaString(meta) {
            if (typeof meta == 'string') {
                meta = JSON.parse(meta);
            }
            return _.map(meta, (val, key, index) => {
                return `${key}: ${val}`;
            }).join(' | ');
        },
        giftCard(id) {
            window.location.href = `/admin/resources/gift-cards/${id}`;
            //Nova.visit(`/csv-import/review/${self.file}`);
        },

        openEditInventory(prod) {
            this.updateInventoryProd = prod;
            this.showUpdateInventory = true;
        },

        updateInventoryDeduct() {
            axios
                .post('/nova-custom-api/update-inventory-deduct', {
                    product: this.updateInventoryProd,
                    orderId: this.resourceId,
                })
                .then(({ data }) => {
                    Nova.success('You successfully updated the quantity and resent the order to POS');
                })
                .catch((error) => {
                    Nova.error('Something went wrong');
                });
            this.showUpdateInventory = false;
            this.updateInventoryProd = {};
        },
        currency(value){
            if(value){
                return new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(value)
            }
        }
    },
};
</script>


<style scoped>
.or-products-wrapper{
    border: #E3E7EB solid 1px;
    border-radius: 5px;
}
.sing-prod-wrap{
    padding: 20px;
    border-bottom: #EFF1F4 solid 1px;
}
.or-single-prod{
    display: flex;
    align-items: center;
}
.sing-prod-wrap:last-child{
    border-bottom: none;
}
.or-img{
    height: 72px;
    width: 72px;
    align-self: flex-start;
}
.or-img img{
    height: 100%;
    width: 100%;
    object-fit: contain;
}
.or-prod-info{
    margin-left: 15px;
    margin-right: 15px;
    display: flex;
    flex-direction: column;
    text-overflow: ellipsis;
    width: 50%;
}
.or-prod-title{
    color: #4099DE;
    font-size: 17px;
    line-height: 22px;
    cursor: pointer;
}
.or-prod-title-stock{
    color: #4099DE;
    font-size: 12px;
    line-height: 22px;
}
.or-prod-sku{
    margin-top: 6px;
    color: #A8ADB4;
    font-size: 14px;
    font-weight: 300;
}
.or-prod-gc{
    margin-top: 6px;
    color: #4099DE;
    font-size: 14px;
    font-weight: 300;
    cursor: pointer;
}
.txt-returned,.txt-cancelled{
    margin-top: 6px;
    color: #F2C766;
    font-size: 14px;
    font-weight: 500;
}
.txt-cancelled{
    color: red;
}
.or-prod-price{
    margin-right: 40px;
    color: #525860;
    font-size: 18px;
    font-weight: 500;
    width: 25%;
}
.gift-option{
    margin-top: 15px;
    margin-left: 85px;
    font-size: 14px;
    color: black;
    font-weight: 500;
}
.gift-option span{
    color: #4099DE;
    cursor: pointer;
    margin-left: 10px;
}
.or-prod-price:last-child{
    margin-right: 10px;
    width: initial;
}



.fixed-here {
    position: fixed;
    height: 100%;
    width: 100%;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    display: flex;
    justify-content: center;
    background-color: rgba(179, 185, 191, 0.8);
    z-index: 100;
    padding: 50px 0;
}

.box-wrapper-here{
    height: 345px;
    overflow: auto;
    width: 650px;
}
</style>
