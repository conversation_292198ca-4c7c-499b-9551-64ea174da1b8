<template>
    <span>
        <!-- <span :key="this.$route.fullPath"> -->
        <div v-if="noSearchResults">
            <no-search-results />
        </div>
        <span v-else>
            <div class="products_wrapper">
                <products-sidebar ref="sideBar" :data="data"></products-sidebar>
                <div class="products_content_wrapper">
                    <div class="prc_center">
                        <products-assistant
                            ref="assistant"
                        ></products-assistant>
                        <div class="products_grid_wrapper">
                            <products-grid
                                v-for="product in products"
                                :key="product.id"
                                :product="product"
                            >
                                <template v-slot:variation-grid>
                                    <variations-product-grid
                                        :product="product"
                                        @hover="
                                            (image) => (product.image = image)
                                        "
                                        @leave="
                                            (image) => (product.image = image)
                                        "
                                    />
                                </template>
                            </products-grid>
                        </div>
                        <products-pagination
                            v-if="data.products"
                            :data="data.products"
                        />
                    </div>
                </div>
            </div>
        </span>
    </span>
</template>

<script>
import Pagination from 'laravel-vue-pagination';
import { mapActions, mapMutations } from 'vuex';
import state from './state';
import Vue from 'vue';
import LoadingSpinner from '../partials/LoadingSpinner';
import ProductsSidebar from './components/ProductsSidebar';
import ProductsAssistant from './components/ProductsAssistant';
import MobileSortFilter from '../layouts/MobileSortFilter';
import SortByOther from '../products/components/SortByOther';
import MobileBreadcrumbsResults from '../layouts/MobileBreadcrumbsResults';
import ProductLanguageOption from './components/ProductLanguageOption';
import ProductsGrid from './components/ProductsGrid';
import ProductsPagination from './components/ProductsPagination';
import VariationsProductGrid from './components/VariationsProductGrid';
import NoSearchResults from './NoSearchResults.vue';

export default {
    components: {
        Pagination,
        LoadingSpinner,
        ProductsSidebar,
        ProductsAssistant,
        MobileSortFilter,
        MobileBreadcrumbsResults,
        ProductLanguageOption,
        ProductsGrid,
        ProductsPagination,
        VariationsProductGrid,
        NoSearchResults,
    },
    //mixins:[sideBar],
    data() {
        return {
            componentName: 'ProductIndex',
            state,
            data: {},
            products: [],
            all: [],
            noSearchResults: false,
            localBus: new Vue(),
            title: '',
        };
    },

    mounted() {
        this.noSearchResults = false;
        this.getProducts();
    },

    methods: {
        ...mapMutations({
            createBreadcrumbs: 'breadcrumbs/CREATE_BREADCRUMBS',
            clearBraedcrumbs: 'breadcrumbs/CLEAR_BREADCRUMBS',
        }),

        getProducts(page = 1) {
            this.noSearchResults = false;
            this.data = {};
            this.products = [];
            if(this.$refs['assistant']) this.$refs['assistant'].setTimer();
            this.$root.$refs['spinner'].startSpinner();

            let pageQuery = _.isEmpty(this.$route.query)
                ? '?page=1'
                : this.$route.query.page
                ? ''
                : '&page=1';

            axios('/api' + this.$route.fullPath + pageQuery)
                .then((success) => {
                    this.data = success.data;
                    this.products = this.data.products.data;

                    this.hasSearchResults();
                    if (this.data.breadcrumbs.length > 0) {
                        this.createBreadcrumbs(this.data.breadcrumbs);
                        this.setTitle();
                    } else {
                        this.clearBraedcrumbs();
                    }

                    this.setTags();
                    this.setSchema();

                    this.state.data.hebrewProducts = this.products.find(
                        (p) => p.heb_title
                    );

                    // Emit productsFinishedLoading event for sorting components
                    // This ensures sorting works even when sidebar is not shown
                    this.componentsComunaction.$emit('productsFinishedLoading', {
                        sideBar: this.$refs.sideBar,
                        requestApi: this.getProducts.bind(this)
                    });
                })
                .catch((error) => {
                    this.$root.$refs['spinner'].stopSpinner();
                    handleError(error, this.$router);
                })
                .then(() => this.$root.$refs['spinner'].stopSpinner());
        },

        hasSearchResults() {
            // Always emit hasSearchResults event for all page types
            if (_.get(this.$route.query, 'q')) {
                // For search pages, check if there are results
                if (!this.products.length > 0) {
                    this.componentsComunaction.$emit('hasSearchResults', false);
                    this.noSearchResults = true;
                } else {
                    this.componentsComunaction.$emit('hasSearchResults', true);
                    this.noSearchResults = false;
                }
            } else {
                // For non-search pages (categories, automated categories, collections)
                // Always emit true if there are products
                if (this.products.length > 0) {
                    this.componentsComunaction.$emit('hasSearchResults', true);
                    this.noSearchResults = false;
                } else {
                    this.componentsComunaction.$emit('hasSearchResults', false);
                    this.noSearchResults = true;
                }
            }
        },

        updateProducts(data) {
            this.data = data;
            if (this.data.breadcrumbs && this.data.breadcrumbs.length > 0) {
                this.createBreadcrumbs(this.data.breadcrumbs);
                this.setTitle();
            }
            this.products = data.products.data;
            this.state.data.hebrewProducts = this.products.find(
                (p) => p.heb_title
            );
        },

        array_combinations(
            data,
            all = [],
            group = [],
            value = null,
            i = 0,
            j = 0
        ) {
            let keys = Object.keys(data);
            if (value != null) {
                group.push(value);
            }
            if (i >= data.length) {
                all.push(group);
            } else {
                let currentKey = keys[i];
                let currentElement = data[currentKey];

                if (currentElement.length) {
                    currentElement.forEach((val) => {
                        this.array_combinations(
                            data,
                            all,
                            group,
                            val,
                            i + 1,
                            j + 1
                        );
                    });
                }
            }
            return all;
        },

        setTitle() {
            this.title = this.$route.query.q
                ? this.$route.query.q
                : this.data.breadcrumbs.slice().pop().name;
            this.title += ' | Eichlers';
            document.title = this.title;
        },

        setSchema() {
            let schema = `{"@context":"http://schema.org",
											"@type":"BreadcrumbList",
                                            "itemListElement":[`;
            let wentIn = false;

            if (_.get(this.data, 'breadcrumbs')) {
                this.data.breadcrumbs.forEach((breadcrumb, index) => {
                    if (_.get(breadcrumb, 'path')) {
                        wentIn = true;
                        schema += `{
                                        "@type": "ListItem",
                                        "position": ${index + 1},
                                        "item":
                                        {
                                            "@type": "${_.get(
                                                breadcrumb,
                                                'name'
                                            )}",
                                            "@id": "${_.get(
                                                breadcrumb,
                                                'path'
                                            )}",
                                            "name": "${_.get(
                                                breadcrumb,
                                                'name'
                                            )}"
                                        }
                                        }`;
                    }
                });
                if (wentIn) {
                    schema += `]}`;
                }
            }

            var element = document.getElementById('brc');
            if (element) element.parentNode.removeChild(element);

            if (schema != '') {
                let script = document.createElement('script');
                script.type = 'application/ld+json';
                script.id = 'brc';
                script.innerHTML = schema;
                document.head.insertBefore(
                    script,
                    document.head.firstElementChild
                );
            }
        },

        setTags() {
            this.createTag(
                'property',
                'og:title',
                _.get(this.data, 'title') || this.title.split('|')[0].trim()
            );
            this.createTag('property', 'og:site_name', '1800eichlers.com');
            this.createTag('property', 'og:type', 'website');
            this.createTag(
                'property',
                'og:url',
                window.location.origin + this.$route.fullPath
            );
            this.createTag('name', 'twitter:card', 'summary');
            this.createTag(
                'name',
                'twitter:site',
                window.location.origin + this.$route.fullPath
            );
            this.createTag(
                'name',
                'twitter:title',
                _.get(this.data, 'title') || this.title.split('|')[0].trim()
            );
            this.createTag(
                'itemprop',
                'name',
                _.get(this.data, 'title') || this.title.split('|')[0].trim()
            );

            this.createTag(
                'property',
                'og:description',
                _.get(this.data, 'description') || ' '
            );
            this.createTag(
                'name',
                'twitter:description',
                _.get(this.data, 'description') || ' '
            );
            this.createTag(
                'itemprop',
                'description',
                _.get(this.data, 'description') || ' '
            );
            this.createTag(
                'name',
                'description',
                _.get(this.data, 'description') || ' '
            );
        },

        createTag(property, value, content) {
            const meta = document.querySelector(
                `[data-meta-id="${property}-${value}"]`
            );
            if (meta) {
                meta.content = content;
            } else {
                var link = document.createElement('meta');
                link.setAttribute(property, value);
                link.setAttribute(`data-meta-id`, `${property}-${value}`);
                link.content = content;
                document.head.insertBefore(
                    link,
                    document.head.firstElementChild
                );
            }
        },
    },

    provide: function () {
        return {
            localBus: this.localBus,
        };
    },

    inject: ['componentsComunaction'],

    created() {
        this.localBus.$on('updateProducts', this.updateProducts);
    },

    beforeDestroy() {
        this.state.clearState();
        this.clearBraedcrumbs();
    },

    watch: {
        $route: {
            handler(newVal, oldVal) {
                this.getProducts();
            },
            deep: true,
        },
    },
};
</script>
