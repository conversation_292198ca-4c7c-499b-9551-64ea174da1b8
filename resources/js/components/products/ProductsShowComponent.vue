<template key="1">
    <span v-if="!_.isEmpty(product)">
        <div class="large_product_wrapper">
            <div class="boxed_width">
                <div
                    class="large_product_top"
                    :style="{ 'padding-bottom': '14px' }"
                >
                    <product-mobile-title
                        :vendor="_.get(product, 'vendor')"
                        :productTitle="
                            state.data.hebrewMode
                                ? product.heb_title || product.title
                                : product.title
                        "
                        :creators="product.creators"
                        :label="getCurrentLabel()"
                        :releaseDate="product.release_date"
                    />

                    <product-large-images
                        ref="largeImages"
                        v-if="current_product.media_urls"
                        :urls="current_product.media_urls"
                        :product="product"
                    ></product-large-images>

                    <div class="large_product_right">
                        <product-vendor
                            v-if="product.vendor"
                            :vendor="product.vendor"
                        ></product-vendor>

                        <h1
                            class="large_product_title"
                            :class="{
                                'hebrew-text':
                                    state.data.hebrewMode && product.heb_title,
                            }"
                        >
                            {{
                                state.data.hebrewMode
                                    ? product.heb_title || product.title
                                    : product.title
                            }}
                        </h1>

                        <product-creator
                            v-if="
                                product.creators && product.creators.length > 0
                            "
                            :creators="product.creators"
                            :releaseDate="product.release_date"
                        >
                        </product-creator>

                        <product-labels
                            :label="getCurrentLabel()"
                            v-if="getCurrentLabel()"
                        ></product-labels>

                        <product-price
                            :price="state.getCurrent().price || ''"
                            :fakePrice="state.getCurrent().fakePrice || ''"
                            :showBundles="showBundles"
                            :bundleItems="bundleItems"
                        />

                        <product-links
                            v-if="product.links && product.links.length > 0"
                            :links="product.links"
                            :product="product"
                        />

                        <product-variations
                            :product="product"
                            v-if="product.variations"
                        ></product-variations>

                        <personlize-index
                            v-if="product.personalizations"
                            :personalizations="product.personalizations"
                            @isPersonalize="(data) => (isPersanalize = data)"
                        />

                        <subscribe
                            v-if="_.get(product, 'subscriptions')"
                            :subscriptions="product.subscriptions"
                        />

                        <recurring-index
                            v-if="
                                product.recurring_array &&
                                product.recurring_array.length > 0
                            "
                            :recurring_array="product.recurring_array"
                        />

                        <div
                            v-if="
                                (product.heb_short_desc &&
                                    state.data.hebrewMode) ||
                                (product.short_desc && !state.data.hebrewMode)
                            "
                            :class="{ 'hebrew-text': state.data.hebrewMode }"
                            class="large_product_highlight large_product_section"
                            v-html="
                                state.data.hebrewMode
                                    ? product.heb_short_desc ||
                                      product.short_desc
                                    : product.short_desc
                            "
                        ></div>

                        <product-add-ons
                            :current_product="current_product"
                            :add_ons="current_product.front_end_add_ons"
                            v-if="
                                current_product.front_end_add_ons &&
                                current_product.front_end_add_ons.length > 0
                            "
                        />

                        <product-personalize v-if="false" />

                        <span v-if="!isPersanalize">
                            <product-digital
                                v-if="
                                    _.get(state.data.variation, 'item_type') ==
                                        'digital' ||
                                    product.item_type == 'digital'
                                "
                                :digital_extensions="product.digital_extensions"
                            />

                            <product-service
                                v-else-if="
                                    _.get(state.data.variation, 'item_type') ==
                                        'service' ||
                                    product.item_type == 'service'
                                "
                            />
                            <product-shipping
                                v-else
                                :release_date="product.release_date"
                                :preorder_date="state.getCurrent().preorder_date"
                                :exclude_free_shipping="
                                    product.exclude_free_shipping
                                "
                                :exclude_from_returns="
                                    product.exclude_from_returns
                                "
                            />
                        </span>

                        <product-cart
                            v-if="!isPersanalize"
                            :product="current_product"
                            :related="related"
                        ></product-cart>

                        <product-follow
                            v-if="
                                product.creators && product.creators.length > 0
                            "
                            :creators="product.creators"
                            :vendor="product.vendor"
                            :vendorType="vendorType"
                        />
                    </div>
                </div>
            </div>

            <div style="background: #faf8f5">
                <frequently-bought-together :product="product" />

                <product-overview
                    v-if="
                        product.description ||
                        (product.heb_description && state.data.hebrewMode)
                    "
                    label="Overview"
                    :hebMode="state.data.hebrewMode"
                    :isHebDescription="product.heb_description || false"
                    :overview="
                        state.data.hebrewMode
                            ? product.heb_description || product.description
                            : product.description
                    "
                >
                </product-overview>
            </div>
        </div>

        <product-bundle
            v-if="showBundles"
            :products="product.front_end_products"
        />

        <frequently-track :product_id="product.id"></frequently-track>

        <related
            :productId="product.id"
            :productType="product.type"
            ref="related"
            @related="getRelated"
        />

        <product-detail
            :product="product"
            :vendorType="vendorType"
        ></product-detail>

        <product-author-biography
            v-if="product.creators && product.creators.length > 0"
            :creators="product.creators"
            :vendorType="vendorType"
        />

        <recent-viewed :product="product" />

        <product-lightbox ref="product-lightbox"></product-lightbox>
        <product-size-chart ref="sizeChart"></product-size-chart>
        <zip-look-up ref="zipLookUp"></zip-look-up>
        <!-- <product-pdf-viewer ref="pdfViewer"></product-pdf-viewer> -->
    </span>
</template>

<script>
import state from './state.js';
import { mapMutations, mapActions } from 'vuex';
import AudioPlayer from './components/media/ProductAudio';
import ZipLookUp from '../checkout/ZipLookUp';
import ProductSizeChart from './components/ProductSizeChart';
import ProductLightbox from './components/ProductLightbox';
import RecentViewed from './RecentViewed';
import ProductAuthorBiography from './components/ProductAuthorBiography';
import ProductDetail from './components/ProductDetail';
import Related from './Related';
import FrequentlyTrack from './FrequentlyTrack';
import ProductBundle from './components/ProductBundle';
import ProductOverview from './components/ProductOverview';
import ProductMobileTitle from './components/ProductMobileTitle';
import ProductLargeImages from './components/ProductLargeImages';
import ProductVendor from './components/ProductVendor';
import ProductCreator from './components/ProductCreator';
import ProductLabels from './components/ProductLabels';
import ProductPrice from './components/ProductPrice';
import ProductLinks from './components/ProductLinks';
import ProductVariations from './components/variations/ProductVariations';
import PersonlizeIndex from '../products/components/personalize/PersonlizeIndex';
import Subscribe from '../subscription/Subscribe';
import RecurringIndex from '../recurring/RecurringIndex';
import ProductAddOns from './components/ProductAddOns';
import ProductPersonalize from './components/ProductPersonalize';
import ProductDigital from './components/ProductDigital';
import ProductService from './components/ProductService';
import ProductShipping from './components/ProductShipping';
import ProductCart from './components/ProductCart';
import ProductFollow from './components/ProductFollow';
import FrequentlyBoughtTogether from './FrequentlyBoughtTogether';
import fakeProduct from './test';
export default {
    data() {
        return {
            name: 'Product Show',
            state,
            related: [],
            product: {},
            variation: {},
            isPersanalize: false,
        };
    },
    mounted() {
        this.getProduct();
    },

    computed: {
        current_product() {
            return this.state.getProduct();
        },

        vendorType() {
            let type;
            if (this.product.creators && this.product.creators.length > 0) {
                switch (this.product.creators[0].type.toLowerCase()) {
                    case 'author':
                        type = 'Publisher';
                        break;
                    case 'singer':
                        type = 'Distributor';
                        break;
                    case 'performer':
                        type = 'Distributor';
                        break;
                    case 'artist':
                        type = 'Distributor';
                }
                return type;
            }
        },

        // bundle properties
        showBundles() {
            return _.get(this.product, 'show_products') == true;
        },
        bundleItems() {
            return _.get(this.product, 'front_end_products')
                ? this.product.front_end_products
                      .map((p) => p.quantity)
                      .reduce((prev, curr) => prev + curr, 0)
                : '';
        },
    },
    methods: {
        ...mapMutations({
            createBreadcrumbs: 'breadcrumbs/CREATE_BREADCRUMBS',
            clearBraedcrumbs: 'breadcrumbs/CLEAR_BREADCRUMBS',
        }),
        ...mapActions({
            setProductContext: 'shippingRates/setProductContext',
            clearProductContext: 'shippingRates/clearProductContext',
        }),

        getCurrentLabel() {
            // If there's a current variation and it has a label, use it
            const currentVariation = this.state.getVariation();
            if (currentVariation && currentVariation.label) {
                return currentVariation.label;
            }

            // Otherwise, fall back to the product's label
            return this.product.label;
        },

        checkForDefaultImg() {
            if (
                _.get(this.product, 'media_urls') &&
                this.product.media_urls.length == 0
            ) {
                this.product.media_urls.push({
                    grid: '/img/default_image.png',
                    large: '/img/default_image.png',
                    lightbox: '/img/default_image.png',
                    thumbnail: '/img/default_image.png',
                });
            }
        },

        getProduct() {
            this.clearState();
            this.$root.$refs['spinner'].startSpinner();
            //testing
            // setTimeout(()=>{
            //   this.state.setProduct(fakeProduct);
            //   this.product = this.state.getProduct();
            //   if(this.product.breadcrumbs.length){this.createBreadcrumbs(this.product.breadcrumbs)}
            //   this.variation = this.state.getVariation();
            //   this.$root.$refs["spinner"].stopSpinner()
            // })

            axios('/api' + this.$route.path)
                .then((success) => {
                    this.state.setProduct(success.data);
                    this.product = this.state.getProduct();

                    try {
                        this.klaviyoViewProduct();
                        this.setSchema();
                        this.setTags();
                        document.title =
                            this.state.getProduct().title + ' | Eichlers';
                    } catch (error) {
                        console.log(error);
                    }

                    if (this.product.breadcrumbs) {
                        this.createBreadcrumbs(this.product.breadcrumbs);
                    }

                    this.variation = this.state.getVariation();

                    // Set product context for shipping calculations
                    this.setProductContext({
                        product_id: this.product.id
                    });

                    this.checkForDefaultImg();
                })
                .catch((err) => {
                    console.log(err);

                    this.$root.$refs['spinner'].stopSpinner();
                    handleError(err, this.$router);
                })
                .then(() => this.$root.$refs['spinner'].stopSpinner());
        },

        getRelated(e) {
            this.related = e;
        },

        clearState() {
            this.state.clearState();
            this.clearBraedcrumbs();
            this.clearProductContext();
            this.product = {};
        },

        setSchema() {
            let schema = `
              {
                "@context": "https://schema.org",
                "@type": "Product",

                 "description": "${
                     _.get(this.product, 'description')
                         ? this.product.description.replace(/(<([^>]+)>)/gi, '')
                         : ''
                 }",

                "name": "${_.get(this.product, 'title')}",
                "image": "${_.get(this.product, 'media_urls[0].grid')}",
                "sku": "${_.get(this.product, 'sku')}",
                "mpn": "${_.get(this.product, 'sku')}",
                "brand": "${_.get(this.product, 'vendor.name')}",
                "offers": {
                  "@type": "Offer",
                    "availability": "${_.get(this.product, "max") > 0
                        ? "https://schema.org/InStock"
                        : "https://schema.org/OutOfStock"
                    }",
                  "price": ${_.get(this.product, 'price')},
                  "priceCurrency": "USD",
                  "potentialAction":{"@type":"BuyAction"},
                  "url": "${_.get(this.product, 'path')}"
                }},
                {"@context": "https://schema.org",
                "@type": "BreadcrumbList",
                "itemListElement":
              `;

            if (_.get(this.product, 'breadcrumbs')) {
                this.product.breadcrumbs.forEach((breadcrumb, index) => {
                    schema += `{
                          "@type": "ListItem",
                          "position": ${index},
                          "item":
                          {
                            "@id": "${_.get(breadcrumb, 'path')}",
                            "name": "${_.get(breadcrumb, 'name')}"
                          }
                        }`;
                });
                schema += `}`;
            }

            if (_.get(this.product, 'creators')) {
                this.product.creators.forEach((creator) => {
                    schema += `{
                        "@context": "http://schema.org/",
                        "@type": "Person",
                        "name": "${creator.name}"
                      }`;
                });
            }
            var element = document.getElementById('brc');
            if (element) element.parentNode.removeChild(element);

            let script = document.createElement('script');
            script.type = 'application/ld+json';
            script.innerHTML = schema;
            script.id = 'brc';
            document.head.insertBefore(script, document.head.firstElementChild);
        },

        setTags() {
            this.createTag(
                'property',
                'og:title',
                _.get(this.product, 'seo_title') || _.get(this.product, 'title')
            );
            this.createTag('property', 'og:site_name', '1800eichlers.com');
            this.createTag('property', 'og:type', 'website');
            this.createTag(
                'property',
                'og:url',
                window.location.origin + _.get(this.product, 'path')
            );
            this.createTag(
                'property',
                'og:image',
                _.get(this.product, 'media_urls[0].grid')
            );

            let description;
            if (_.get(this.product, 'seo_desc'))
                description = this.product.seo_desc;
            else if (_.get(this.product, 'description'))
                description = this.product.description.replace(
                    /(<([^>]+)>)/gi,
                    ''
                );
            if (description) {
                this.createTag('property', 'og:description', description);
                this.createTag('name', 'twitter:description', description);
                this.createTag('itemprop', 'description', description);
                this.createTag('name', 'description', description);
            } else {
                this.createTag('property', 'og:description', '');
                this.createTag('name', 'twitter:description', '');
                this.createTag('itemprop', 'description', '');
                this.createTag('name', 'description', '');
            }

            this.createTag('name', 'twitter:card', 'summary');
            this.createTag(
                'name',
                'twitter:site',
                window.location.origin + _.get(this.product, 'path')
            );
            this.createTag(
                'name',
                'twitter:title',
                _.get(this.product, 'seo_title') || _.get(this.product, 'title')
            );
            this.createTag(
                'name',
                'twitter:image:src',
                _.get(this.product, 'media_urls[0].grid')
            );
            this.createTag(
                'itemprop',
                'name',
                _.get(this.product, 'seo_title') || _.get(this.product, 'title')
            );
            this.createTag(
                'itemprop',
                'image',
                _.get(this.product, 'media_urls[0].grid')
            );
        },

        createTag(property, value, content) {
            const meta = document.querySelector(
                `[data-meta-id="${property}-${value}"]`
            );
            if (meta) {
                meta.content = content;
            } else {
                var link = document.createElement('meta');
                link.setAttribute(property, value);
                link.setAttribute(`data-meta-id`, `${property}-${value}`);
                link.content = content;
                document.head.insertBefore(
                    link,
                    document.head.firstElementChild
                );
            }
        },
        klaviyoViewProduct() {
            if (window.klaviyo) {
                const item = {
                    "ProductName": this.product.title,
                    "ProductID": this.product.id,
                    "SKU": this.product.sku,
                    "Categories": this.product.categories
                        ? this.product.categories.map((c) => c.name)
                        : [],
                    "ImageURL": this.product?.media_urls?.[0]?.grid || '',
                    "URL": window.location.href,
                    "Brand": this.product.vendor?.name || '',
                    "Price": this.product.price || 0,
                    "SalePrice": this.product.sale_price || null,
                };
                window.klaviyo.track('Viewed Product', item);
            }
        },
    },

    beforeDestroy() {
        this.clearState();
    },

    watch: {
        $route: {
            handler(oldVal, newVal) {
                if (oldVal.path !== newVal.path) {
                    this.getProduct();
                }
            },
            deep: true,
        },
    },

    components: {
        ProductFollow,
        AudioPlayer,
        ZipLookUp,
        ProductSizeChart,
        ProductLightbox,
        RecentViewed,
        ProductAuthorBiography,
        ProductDetail,
        Related,
        FrequentlyTrack,
        ProductBundle,
        ProductOverview,
        ProductMobileTitle,
        ProductLargeImages,
        ProductVendor,
        ProductCreator,
        ProductLabels,
        ProductPrice,
        ProductLinks,
        ProductVariations,
        PersonlizeIndex,
        Subscribe,
        RecurringIndex,
        ProductAddOns,
        ProductPersonalize,
        ProductDigital,
        ProductService,
        ProductShipping,
        ProductCart,
        FrequentlyBoughtTogether,
    },
};
</script>
<style scoped>
.hebrew-text {
    direction: rtl;
    text-align: left;
    font-family: heebo;
}
</style>
