<template>
    <div v-if="currentZip">
        <div
            class="large_product_ship_section large_product_section"
            :class="{ 'no-border': personlizedShippingOptions }"
        >
            <div class="ship_icon">
                <img src="/img/ship_outline.svg" />
            </div>
            <div class="ship_info_side">
                <p class="ship_top_text">
                    Shipping to
                    <span @click="lookUpZip()">{{ currentZip }}</span>
                </p>

                <span v-if="(freeOver || cheapest || fastest) && !isPreOrder">
                    <!-- free shipping over -->
                    <div
                        v-if="freeOver && !exclude_free_shipping"
                        class="ship_option"
                    >
                        <p class="ship_option_name">{{ freeOver.msg }}</p>
                        <p class="ship_option_sub">
                            with
                            <span>Free shipping</span> over {{ freeOver.over }}
                        </p>
                    </div>

                    <!-- cheapest shipping option -->
                    <div
                        class="ship_option"
                        v-if="
                            cheapest &&
                            (cheapest.id != _.get(freeOver, 'id') ||
                                exclude_free_shipping)
                        "
                    >
                        <p class="ship_option_name">{{ cheapest.msg }}</p>
                        <p class="ship_option_sub">
                            with
                            <span>{{ cheapest.name }}</span> at Checkout
                        </p>
                    </div>

                    <!-- fastest shipping option -->
                    <div
                        class="ship_option"
                        v-if="
                            fastest &&
                            fastest.id != _.get(cheapest, 'id') &&
                            (fastest.id != _.get(freeOver, 'id') ||
                                exclude_free_shipping)
                        "
                    >
                        <p class="ship_option_name">{{ fastest.msg }}</p>
                        <p class="ship_option_sub">
                            with
                            <span>{{ fastest.name }}</span> at Checkout
                        </p>
                    </div>

                    <div v-if="exclude_free_shipping" class="ship_option">
                        <p class="ship_option_sub" style="color:red;">
                            This item ships free only in areas where Local Delivery is available. This item is not eligible for free returns.
                        </p>
                    </div>

                    <div v-if="exclude_from_returns" class="ship_option">
                        <p class="ship_option_sub" style="color:red;">
                            This item in not eligible for returns/exchanges unless damaged or defective.
                        </p>
                    </div>
                </span>

                <span v-else-if="isPreOrder">
                    <span class="sh-not-supp"
                        >Ships after {{ preorder_date ?? release_date | mmddyyyy }}</span
                    >
                </span>
                <span v-else>
                    <span class="sh-not-supp"
                        >We currently don’t support shipping to
                        {{ currentZip }}</span
                    >
                </span>
            </div>
        </div>
        <div
            class="large_product_ship_section large_product_pickup_section large_product_section"
            v-if="pickupWithin && !isPreOrder"
            :class="{ 'no-border': personlizedShippingOptions }"
        >
            <div class="ship_icon pickup_icon">
                <img src="/img/bag_outline.svg" />
            </div>
            <div class="ship_info_side">
                <p class="ship_top_text pickup_top_text">
                    Free in store Pick up
                    <br />
                    <span>{{ pickupWithin }}</span>
                </p>
            </div>
        </div>
    </div>

    <div
        v-else
        class="large_product_ship_section large_product_section"
        :class="{ 'no-border': personlizedShippingOptions }"
    >
        <div class="ship_icon">
            <img src="/img/ship_outline.svg" />
        </div>
        <div class="ship_info_side">
            <p class="ship-top-text">Shipping</p>
            <p class="ship_option_sub">
                Add your delivery location to get accurate delivery & pickup
                information.
            </p>

            <p @click="lookUpZip()" class="btn-zip">Add Location</p>
        </div>
    </div>
</template>

<script>
import extendedDuration from '../extendedDuration';
import { mapGetters, mapActions } from 'vuex';
export default {
    mixins: [extendedDuration],

    props: [
        'exclude_free_shipping',
        'exclude_from_returns',
        'personlizedShippingOptions',
        'preorder_date',
        'release_date',
    ],

    computed: {
        ...mapGetters({
            currentZip: 'zipLookUp/getZipCode',
            location: 'zipLookUp/getLocation',
            _pickupWithin: 'shippingRates/pickupWithin',
            _freeOver: 'shippingRates/freeOver',
            _cheapest: 'shippingRates/cheapest',
            _fastest: 'shippingRates/fastest',
        }),

        freeOver() {
            if (this.extendedFreeOver) return this.extendedFreeOver;

            return this.personlizedShippingOptions
                ? this.personlizedShippingOptions.per_free_over
                : this._freeOver;
        },

        cheapest() {
            if (this.extendedCheapest) return this.extendedCheapest;

            return this.personlizedShippingOptions
                ? this.personlizedShippingOptions.per_cheapest
                : this._cheapest;
        },

        fastest() {
            if (this.extendedFastest) return this.extendedFastest;

            return this.personlizedShippingOptions
                ? this.personlizedShippingOptions.per_fastest
                : this._fastest;
        },

        pickupWithin() {
            if (this.extendedPickup) return this.extendedPickup;

            return this.personlizedShippingOptions
                ? this.personlizedShippingOptions.per_pickup
                : this._pickupWithin;
        },

        isPreOrder() {
            if (this.preorder_date){
                return true;
            } else if (this.release_date) {
                return this.release_date > new Date();
            } else {
                return false;
            }
        },
    },

    methods: {
        lookUpZip() {
            this.$parent.$refs.zipLookUp.open();
        },
        ...mapActions({
            updateShippingOptions: 'shippingRates/requestShippingOptions',
        }),
    },
    watch: {
        location: {
            handler() {
                // The store will automatically detect product context from multiple sources
                this.updateShippingOptions(this.currentZip);
            },
            deep: true,
        },
    },
};
</script>

<style scoped>
.ship-top-text {
    font-weight: 500;
    margin-bottom: 8px;
}
.btn-zip {
    margin-top: 8px;
    font-size: 16px;
    color: #016145;
    padding: 8px 10px;
    background-color: #fff;
    border: solid #016145 1px;
    width: 145px;
    display: flex;
    justify-content: center;
    border-radius: 1px;
    transition: 30ms ease;
}
.btn-zip:hover {
    cursor: pointer;
    background-color: #016145;
    color: #fff;
}
.sh-not-supp {
    color: black;
    font-weight: 300;
    font-size: 17px;
}
.no-border {
    border-bottom: initial;
}
</style>
