<script>
import CreatorService from "../services/CreatorService.js";
import ProductTrack from "../components/products/components/ProductTrack.vue";

export default {
    name: "CreatorSingleView",
    components: {ProductTrack},
    data: () => {
        return {
            creator: null,
            loading: true
        };
    },
    async mounted() {
        const creatorId = this.$route.params.id;
        await this.fetchCreator(creatorId);
    },
    methods: {
        async fetchCreator(creatorId) {
            try {
                this.loading = true;
                const response = await CreatorService.getCreatorById(creatorId);
                this.creator = response.data;
            } catch (error) {
                console.error('There has been a problem with your fetch operation:', error);
            } finally {
                this.loading = false;
            }
        }
    }
}
</script>

<template>
    <div class="creator-single-view">
        <div v-if="!loading">
            <div class="boxed_width">
                <div class="creator-header">
                    <div class="creator-name">
                        <img v-if="creator.image" :src="creator.image" alt="Creator Image" class="creator-image">
                        <h1>{{ creator.name }}</h1>
                    </div>
                    <a v-if="creator.button_text && creator.button_link"
                       target="_blank"
                       :href="creator.button_link"
                       class="btn">{{ creator.button_text }}</a>
                </div>
            </div>
            <div class="creator-description">
                <div class="boxed_width">
                    <div v-html="creator.description"></div>
                </div>
            </div>
            <product-track
                v-if="creator.products.length > 0"
                :products="creator.products"
                :displayViewAll="true"
                :viewAllLink="`/search?creators=${encodeURIComponent(creator.name)}`"
                label="Author's Products"
            >
            </product-track>
        </div>
        <div v-else>
            <loading-spinner show-force="true"/>
        </div>
    </div>
</template>

<style lang="scss">
.creator-single-view {
    margin: 60px 0;

    .creator-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 20px 0;
        @media screen and (max-width: 768px) {
            flex-direction: column;
        }

        .btn {
            background: var(--gold);
            border: none;
            color: #fff;
            padding: 10px 20px;
            height: 40px;
            width: auto;
            cursor: pointer;
            user-select: none;
            border-radius: 1px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 14px;

            &:hover {
                opacity: .8;
            }
        }
    }

    .creator-name {
        display: flex;
        align-items: center;
        gap: 20px;
        margin: 20px 0;
        @media screen and (max-width: 768px) {
            flex-direction: column;
        }
    }

    .creator-image {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        margin-right: 20px;
        background-color: var(--light_shade);
        object-fit: cover;
    }

    .creator-description {
        width: 100%;
        padding: 35px 25px;
        border-radius: 2px;
        background: var(--light_shade);

        * {
            background: transparent !important;
            font-size: 16px !important;
            font-family: proxima-nova, sans-serif !important;
        }
    }
}
</style>
