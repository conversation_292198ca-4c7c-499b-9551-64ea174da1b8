import moment from 'moment';
export default {
    namespaced: true,
    state: {
        shippingOptions: {},
        estimatedShippingPrice: 0,
        countries: [],
        currencies: [],
        currency: {},
        usdCurrency: {
            currency: 'USD',
            name: 'US Dollar',
            rate: 1,
            symbol: '',
        },
    },
    mutations: {
        INDEX(state, payload) {
            state.shippingOptions = payload;
        },
        UPDATE_SHIPPING_PRICE(state, price) {
            state.estimatedShippingPrice = price;
        },
        GET_COUNTRIES(state, countries) {
            state.countries = countries;
        },
        GET_CURRENCIES(state, currencies) {
            state.currencies = currencies;
            state.currencies.unshift(state.usdCurrency);
        },
        SET_CURRENCY(state, currency) {
            state.currency = currency;
        },
        SET_SERVER_EXPIRE(state, time) {
            if (time != _.get(state, 'server_expires')) {
                _.set(state, 'server_expires', time);
                let zipCode = this.getters['zipLookUp/getZipCode'];
                this.dispatch('shippingRates/requestShippingOptions', zipCode);
            }
        },
    },
    getters: {
        freeShipping: (state) => _.get(state.shippingOptions, 'over_price'),

        hasPickup: (state) => _.get(state.shippingOptions, 'pickup'),

        shippingPrice: (state) => state.estimatedShippingPrice,

        countries: (state) => state.countries,

        currencies: (state) => state.currencies,

        currency: (state) => state.currency,

        shippingOptions: (state) => state.shippingOptions,

        cutoffTime(state) {
            let fulfillment_expires = new Date(
                _.get(state.shippingOptions, 'fulfillment_expires')
            );
            let store_expires = new Date(
                _.get(state.shippingOptions, 'store_expires')
            );
            return fulfillment_expires < store_expires
                ? fulfillment_expires
                : store_expires;
        },

        serverExpires(state) {
            return _.get(state, 'server_expires');
        },

        fastestInDaysHours(state) {
            let fastest = _.get(state.shippingOptions, 'fastest');
            if (fastest) {
                if(fastest.days >= 2) {
                    return `${Math.floor(fastest.days)}-day shipping`;
                }
                return fastest.days >= 1 ? 'Get it Tomorrow' : 'Get it Today';
            }
        },

        pickupWithin(state) {
            let pickup = _.get(state.shippingOptions, 'pickup');
            if (pickup) {
                return pickup.days > 0
                    ? pickup.date
                    : `within ${pickup.hours + 1} ${
                          pickup.hours > 0 ? 'hours' : 'hour'
                      }`;
            }
        },

        freeOver(state) {
            if (!_.isEmpty(state.shippingOptions.free_over)) {
                let over = state.shippingOptions.free_over;
                let e = {};
                if (over.days > 1) {
                    e.msg = `Get it ${formattedDate(over.estimated_arrival)}`;
                } else {
                    e.msg = over.days == 1 ? 'Get it Tomorrow' : 'Get it Today';
                }

                e.over = '$' + over.free_shipping_above;
                e.id = over.id;
                return e;
            }
        },

        cheapest(state, getters) {
            let cheap = _.get(state.shippingOptions, 'cheapest');
            //if(cheap && cheap.id != _.get(state.shippingOptions,'free_over.id') && cheap.id != _.get(state.shippingOptions,'fastest.id') ){
            if (cheap) {
                let e = {};

                if (cheap.days > 1) {
                    e.msg = `Get it ${formattedDate(cheap.estimated_arrival)}`;
                } else {
                    e.msg =
                        cheap.days == 1 ? 'Get it Tomorrow' : 'Get it Today';
                }

                e.name = cheap.name;
                e.id = cheap.id;
                return e;
            }
            // }
        },

        fastest(state, getters) {
            let fast = _.get(state.shippingOptions, 'fastest');
            // if(fast && fast.id != _.get(state.shippingOptions,'free_over.id') && fast.id != _.get(state.shippingOptions,'cheapest.id')){
            if (fast) {
                let e = {};

                if (fast.days > 1) {
                    e.msg = `Get it ${formattedDate(fast.estimated_arrival)}`;
                } else {
                    e.msg = fast.days == 1 ? 'Get it Tomorrow' : 'Get it Today';
                }

                e.name = fast.name;
                e.id = fast.id;
                return e;
            }
            // }
        },

        shippingOptionsIdsAllwaysFreeAbove(state) {
            return (
                _.get(
                    state.shippingOptions,
                    'shipping_ids_not_excluded_from_free_shipping'
                ) || ''
            )
                .split(',')
                .map((id) => Number(id));
        },
    },
    actions: {
        index({ dispatch, getters, rootGetters }) {
            let zipCode = rootGetters['zipLookUp/getZipCode'];
            if (
                (new Date() > getters.cutoffTime ||
                    getters.cutoffTime == 'Invalid Date') &&
                zipCode
            ) {
                dispatch('requestShippingOptions', zipCode);
            }
        },

        requestShippingOptions({ commit }, zip) {
            // Check if we're on a product page and include product context
            let requestData = { zip_code: zip };

            // If we're on a product page, extract product ID from URL for accurate delay calculations
            if (window.location.pathname.includes('/products/')) {
                const pathParts = window.location.pathname.split('/');
                const productId = pathParts[pathParts.length - 1];
                if (productId && !isNaN(productId)) {
                    requestData.product_id = parseInt(productId);
                }
            }

            axios
                .post('/api/shipping/index', requestData)
                .then((resp) => {
                    commit('INDEX', resp.data);
                })
                .catch((err) => {
                    let code = _.get(err, 'response.status');
                    if (code == 404 || code == 400) {
                        commit('INDEX', {});
                    } else {
                        console.error(err);
                    }
                });
        },

        getCountries({ commit }) {
            axios('/api/countries')
                .then((resp) => {
                    commit('GET_COUNTRIES', resp.data);
                })
                .catch((err) => {
                    console.error(err);
                });
        },

        getCurrencies({ commit, getters, state }) {
            axios('/api/currencies')
                .then((resp) => {
                    commit('GET_CURRENCIES', resp.data);
                    if (_.isEmpty(getters.currency)) {
                        commit('SET_CURRENCY', state.usdCurrency);
                    }
                })
                .catch((err) => {
                    console.error(err);
                });
        },
    },
};

function formattedDate(date) {
    return moment(date).format('ddd, MMMM D');
}
