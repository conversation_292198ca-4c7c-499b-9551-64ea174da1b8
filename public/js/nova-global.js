
async function uploadFileFromUrl(url, fileInput) {
    try {
        const response = await fetch(url);
        if (!response.ok) throw new Error('Error downloading file');

        const blob = await response.blob();

        const fileName = url.substring(url.lastIndexOf('/') + 1);

        const file = new File([blob], fileName, { type: blob.type });

        if (!fileInput) throw new Error('File input not found');

        const dataTransfer = new DataTransfer();
        dataTransfer.items.add(file);
        fileInput.files = dataTransfer.files;

        const changeEvent = new Event('change', { bubbles: true });
        fileInput.dispatchEvent(changeEvent);

        console.log('File uploaded successfully');
    } catch (error) {
        console.error('Ошибка:', error.message);
    }
}

document.addEventListener('DOMContentLoaded', () => {
    const targetNode = document.querySelector('#nova');

    if (!targetNode) return;


    const observer = new MutationObserver((mutationsList) => {
        for (const mutation of mutationsList) {
            if (mutation.type === 'childList') {
                const mediaFields = document.querySelectorAll('#nova .gallery');
                if (mediaFields.length > 0) {
                    mediaFields.forEach(field => {
                        const input = field.querySelector('input[type="file"]');
                        if (!input.dataset.urlAdded) {
                            addUrlField(input);
                            input.dataset.urlAdded = 'true';
                        }
                    });
                }
            }
        }
    });

    observer.observe(targetNode, {
        childList: true,
        subtree: true
    });

    function addUrlField(field) {
        const input = document.createElement('input');
        input.type = 'text';
        input.placeholder = 'Enter URL';
        input.className = 'flex-1 relative mt-2 focus:border-blue focus:shadow form-control form-input form-input-bordered';
        field.parentNode.append(input);
        input.addEventListener('keyup', (e) => {
            if (e.key === 'Enter') {
                uploadFileFromUrl(input.value, field).finally(() => {
                    input.value = '';
                });
            }
        });
    }
});
